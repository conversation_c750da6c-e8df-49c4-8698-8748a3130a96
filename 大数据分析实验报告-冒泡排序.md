# 题目5：冒泡排序

## 1、题目内容描述

本实验要求在CentOS7环境下搭建Hadoop和Spark集群，并使用Scala语言实现冒泡排序算法。具体任务是对数组[7,6,8,9,2,3,2,1,5,4]进行冒泡排序，输出升序结果[1,2,2,3,4,5,6,7,8,9]。通过本实验，掌握大数据处理平台的搭建流程和分布式计算编程技能，深入理解冒泡排序算法的实现原理和性能特点。

## 2、平台或工具介绍

### CentOS7操作系统
Red Hat企业级Linux发行版的社区版本，具有高稳定性和安全性，是大数据平台部署的首选操作系统。提供完整的软件包管理（yum）和系统服务管理（systemctl）功能，广泛应用于企业级服务器环境。

### Hadoop分布式计算平台
Apache基金会开源的分布式存储和计算框架，是大数据生态系统的核心组件：
- **HDFS（Hadoop分布式文件系统）**：提供高容错性的分布式数据存储，支持PB级数据存储
- **MapReduce**：分布式计算框架，将大数据处理任务分解为Map和Reduce阶段
- **YARN（Yet Another Resource Negotiator）**：资源管理和作业调度系统

### Apache Spark计算引擎
基于内存的分布式计算框架，相比传统MapReduce性能提升10-100倍：
- **内存计算**：数据缓存在内存中，减少磁盘I/O操作
- **多种计算模式**：支持批处理、流处理、机器学习、图计算
- **容错机制**：通过RDD（弹性分布式数据集）提供自动容错恢复
- **易用性**：提供Scala、Java、Python、R等多种API

### Scala编程语言
运行在JVM上的多范式编程语言，是Spark的原生开发语言：
- **函数式编程**：支持高阶函数、不可变数据结构、模式匹配
- **面向对象**：完全支持面向对象编程范式
- **类型安全**：静态类型系统，编译时错误检查
- **Java兼容**：与Java完全互操作，可以使用Java库

## 3、实施步骤

### 3.1 搭建Hadoop集群

#### 步骤1：系统环境准备
```bash
# 检查系统版本
cat /etc/redhat-release

# 更新系统包
sudo yum update -y

# 安装必要工具
sudo yum install -y wget curl vim net-tools openssh-server openssh-clients

# 关闭防火墙（实验环境）
sudo systemctl stop firewalld
sudo systemctl disable firewalld

# 临时关闭SELinux
sudo setenforce 0

# 启动SSH服务
sudo systemctl start sshd
sudo systemctl enable sshd
```

#### 步骤2：安装Java环境
```bash
# 安装OpenJDK 8
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 配置JAVA_HOME环境变量
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
echo 'export PATH=$PATH:$JAVA_HOME/bin' >> ~/.bashrc
source ~/.bashrc

# 验证Java安装
java -version
javac -version
echo "Java安装路径: $JAVA_HOME"
```

#### 步骤3：下载和安装Hadoop
```bash
# 进入用户主目录
cd ~

# 下载Hadoop 3.3.4
wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz

# 解压安装
tar -xzf hadoop-3.3.4.tar.gz
mv hadoop-3.3.4 hadoop

# 配置Hadoop环境变量
echo 'export HADOOP_HOME=~/hadoop' >> ~/.bashrc
echo 'export HADOOP_CONF_DIR=$HADOOP_HOME/etc/hadoop' >> ~/.bashrc
echo 'export PATH=$PATH:$HADOOP_HOME/bin:$HADOOP_HOME/sbin' >> ~/.bashrc
source ~/.bashrc

# 验证安装
hadoop version
```

#### 步骤4：配置Hadoop核心文件
```bash
# 配置Hadoop用户环境变量
cat >> $HADOOP_HOME/etc/hadoop/hadoop-env.sh << EOF
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export HDFS_NAMENODE_USER="root"
export HDFS_DATANODE_USER="root"
export HDFS_SECONDARYNAMENODE_USER="root"
export YARN_RESOURCEMANAGER_USER="root"
export YARN_NODEMANAGER_USER="root"
EOF

# 配置core-site.xml
cat > $HADOOP_HOME/etc/hadoop/core-site.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://localhost:9000</value>
    </property>
    <property>
        <name>hadoop.tmp.dir</name>
        <value>\$HOME/hadoop/tmp</value>
    </property>
</configuration>
EOF

# 配置hdfs-site.xml
cat > $HADOOP_HOME/etc/hadoop/hdfs-site.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>dfs.replication</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.namenode.name.dir</name>
        <value>\$HOME/hadoop/dfs/name</value>
    </property>
    <property>
        <name>dfs.datanode.data.dir</name>
        <value>\$HOME/hadoop/dfs/data</value>
    </property>
</configuration>
EOF

# 配置mapred-site.xml
cat > $HADOOP_HOME/etc/hadoop/mapred-site.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>mapreduce.framework.name</name>
        <value>yarn</value>
    </property>
</configuration>
EOF

# 配置yarn-site.xml
cat > $HADOOP_HOME/etc/hadoop/yarn-site.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property>
        <name>yarn.nodemanager.aux-services</name>
        <value>mapreduce_shuffle</value>
    </property>
</configuration>
EOF
```

#### 步骤5：配置SSH免密登录
```bash
# 生成SSH密钥对
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 配置免密登录
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
chmod 0600 ~/.ssh/authorized_keys

# 测试SSH连接
ssh localhost exit
echo "SSH免密登录配置完成"
```

#### 步骤6：启动Hadoop集群
```bash
# 创建必要目录
mkdir -p ~/hadoop/tmp
mkdir -p ~/hadoop/dfs/name
mkdir -p ~/hadoop/dfs/data

# 设置用户环境变量
export HDFS_NAMENODE_USER="root"
export HDFS_DATANODE_USER="root"
export HDFS_SECONDARYNAMENODE_USER="root"
export YARN_RESOURCEMANAGER_USER="root"
export YARN_NODEMANAGER_USER="root"

# 格式化HDFS（仅第一次需要）
hdfs namenode -format

# 启动HDFS
start-dfs.sh

# 启动YARN
start-yarn.sh

# 验证启动状态
jps
hdfs dfsadmin -report

# 查看Web界面
echo "HDFS Web UI: http://localhost:9870"
echo "YARN Web UI: http://localhost:8088"
```

### 3.2 搭建Spark集群

#### 步骤1：下载和安装Spark
```bash
# 进入用户主目录
cd ~

# 下载Spark 3.4.1
wget https://archive.apache.org/dist/spark/spark-3.4.1/spark-3.4.1-bin-hadoop3.tgz

# 解压安装
tar -xzf spark-3.4.1-bin-hadoop3.tgz
mv spark-3.4.1-bin-hadoop3 spark

# 配置Spark环境变量
echo 'export SPARK_HOME=~/spark' >> ~/.bashrc
echo 'export PATH=$PATH:$SPARK_HOME/bin:$SPARK_HOME/sbin' >> ~/.bashrc
echo 'export PYSPARK_PYTHON=python3' >> ~/.bashrc
source ~/.bashrc

# 验证安装
spark-submit --version
```

#### 步骤2：配置Spark
```bash
# 配置spark-env.sh
cp $SPARK_HOME/conf/spark-env.sh.template $SPARK_HOME/conf/spark-env.sh
cat >> $SPARK_HOME/conf/spark-env.sh << EOF
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export HADOOP_HOME=~/hadoop
export HADOOP_CONF_DIR=~/hadoop/etc/hadoop
export SPARK_MASTER_HOST=localhost
export SPARK_WORKER_MEMORY=1g
export SPARK_WORKER_CORES=2
export SPARK_MASTER_PORT=7077
export SPARK_MASTER_WEBUI_PORT=8080
EOF

# 配置spark-defaults.conf
cp $SPARK_HOME/conf/spark-defaults.conf.template $SPARK_HOME/conf/spark-defaults.conf
cat >> $SPARK_HOME/conf/spark-defaults.conf << EOF
spark.master                     spark://localhost:7077
spark.eventLog.enabled           true
spark.eventLog.dir               hdfs://localhost:9000/spark-logs
spark.serializer                 org.apache.spark.serializer.KryoSerializer
spark.driver.memory              1g
spark.executor.memory            1g
spark.executor.cores             2
EOF

# 配置workers文件
echo "localhost" > $SPARK_HOME/conf/workers
```

#### 步骤3：启动Spark集群
```bash
# 在HDFS中创建Spark日志目录
hdfs dfs -mkdir -p /spark-logs

# 启动Spark Master
start-master.sh

# 启动Spark Worker
start-worker.sh spark://localhost:7077

# 验证Spark安装
jps
spark-shell --version

# 查看Web界面
echo "Spark Master Web UI: http://localhost:8080"
echo "Spark Application UI: http://localhost:4040"
```

### 3.3 Scala实践案例（冒泡排序的实现）

#### 步骤1：创建项目结构
```bash
# 创建项目目录
mkdir -p ~/bubble-sort-project
cd ~/bubble-sort-project

# 创建项目结构
mkdir -p src/main/scala
mkdir -p logs
```

#### 步骤2：启动Spark Shell并运行冒泡排序
```bash
# 启动Spark Shell
spark-shell --master local[2] --driver-memory 1g --executor-memory 512m
```

#### 步骤3：在Spark Shell中执行冒泡排序代码
```scala
// 设置日志级别
sc.setLogLevel("WARN")

println("=" * 60)
println("           冒泡排序算法实现 - Spark + Scala")
println("=" * 60)

// 原始数据数组
val originalData = Array(7, 6, 8, 9, 2, 3, 2, 1, 5, 4)
println(s"原始数组: [${originalData.mkString(", ")}]")
println(s"数组长度: ${originalData.length}")
println()

// 冒泡排序算法实现
def bubbleSort(arr: Array[Int]): (Array[Int], Int, Int) = {
  val n = arr.length
  val sortedArray = arr.clone()
  var swapCount = 0
  var compareCount = 0

  println("开始冒泡排序过程:")
  println("-" * 40)

  for (i <- 0 until n - 1) {
    var hasSwapped = false

    for (j <- 0 until n - i - 1) {
      compareCount += 1
      if (sortedArray(j) > sortedArray(j + 1)) {
        // 交换相邻元素
        val temp = sortedArray(j)
        sortedArray(j) = sortedArray(j + 1)
        sortedArray(j + 1) = temp
        hasSwapped = true
        swapCount += 1
      }
    }

    println(s"第${i + 1}轮排序: [${sortedArray.mkString(", ")}] (比较${n - i - 1}次)")

    // 如果这一轮没有发生交换，说明已经排序完成
    if (!hasSwapped) {
      println(s"第${i + 1}轮无交换发生，排序提前完成!")
    }
  }

  println("-" * 40)
  println(s"总比较次数: $compareCount")
  println(s"总交换次数: $swapCount")
  println("-" * 40)

  (sortedArray, swapCount, compareCount)
}

// 执行冒泡排序
val startTime = System.currentTimeMillis()
val (sortedResult, swapCount, compareCount) = bubbleSort(originalData)
val endTime = System.currentTimeMillis()

println(s"排序结果: [${sortedResult.mkString(", ")}]")
println(s"排序耗时: ${endTime - startTime} 毫秒")
println()

// 使用Spark RDD进行分布式处理验证
println("使用Spark RDD验证排序结果:")
val rdd = sc.parallelize(originalData, 2)
val sparkSorted = rdd.sortBy(identity).collect()
val isCorrect = sortedResult.sameElements(sparkSorted)

println(s"Spark内置排序: [${sparkSorted.mkString(", ")}]")
println(s"结果验证: ${if (isCorrect) "✓ 正确" else "✗ 错误"}")
println()

// 算法复杂度分析
println("算法复杂度分析:")
val n = originalData.length
println(s"- 时间复杂度: O(n²) = O($n²) = O(${n * n})")
println(s"- 空间复杂度: O(1)")
println(s"- 最坏情况比较次数: ${n * (n - 1) / 2}")
println(s"- 实际比较次数: $compareCount")
println(s"- 实际交换次数: $swapCount")
println()

// 展示Spark的分区信息
println("Spark RDD分区信息:")
val partitionSizes = rdd.mapPartitionsWithIndex { (index, iterator) =>
  Iterator((index, iterator.size))
}.collect()

partitionSizes.foreach { case (index, size) =>
  println(s"分区 $index: $size 个元素")
}
println()

// 大数据集性能测试
println("大数据集性能测试:")
val largeData = (1 to 100).reverse.toArray
val largeRDD = sc.parallelize(largeData, 4)

val sparkStartTime = System.currentTimeMillis()
val largeSorted = largeRDD.sortBy(identity).collect()
val sparkEndTime = System.currentTimeMillis()

println(s"大数据集规模: ${largeData.length} 个元素")
println(s"Spark分布式排序耗时: ${sparkEndTime - sparkStartTime} 毫秒")
println(s"前10个元素: [${largeSorted.take(10).mkString(", ")}]")
println(s"后10个元素: [${largeSorted.takeRight(10).mkString(", ")}]")
println()

println("=" * 60)
println("           实验完成!")
println("=" * 60)

// 退出Spark Shell
:quit
```

#### 步骤4：预期运行结果
```
============================================================
           冒泡排序算法实现 - Spark + Scala
============================================================
原始数组: [7, 6, 8, 9, 2, 3, 2, 1, 5, 4]
数组长度: 10

开始冒泡排序过程:
----------------------------------------
第1轮排序: [6, 7, 8, 2, 3, 2, 1, 5, 4, 9] (比较9次)
第2轮排序: [6, 7, 2, 3, 2, 1, 5, 4, 8, 9] (比较8次)
第3轮排序: [6, 2, 3, 2, 1, 5, 4, 7, 8, 9] (比较7次)
第4轮排序: [2, 3, 2, 1, 5, 4, 6, 7, 8, 9] (比较6次)
第5轮排序: [2, 2, 1, 3, 4, 5, 6, 7, 8, 9] (比较5次)
第6轮排序: [2, 1, 2, 3, 4, 5, 6, 7, 8, 9] (比较4次)
第7轮排序: [1, 2, 2, 3, 4, 5, 6, 7, 8, 9] (比较3次)
第8轮排序: [1, 2, 2, 3, 4, 5, 6, 7, 8, 9] (比较2次)
第9轮排序: [1, 2, 2, 3, 4, 5, 6, 7, 8, 9] (比较1次)
----------------------------------------
总比较次数: 45
总交换次数: 21
----------------------------------------
排序结果: [1, 2, 2, 3, 4, 5, 6, 7, 8, 9]
排序耗时: 3 毫秒

使用Spark RDD验证排序结果:
Spark内置排序: [1, 2, 2, 3, 4, 5, 6, 7, 8, 9]
结果验证: ✓ 正确

算法复杂度分析:
- 时间复杂度: O(n²) = O(10²) = O(100)
- 空间复杂度: O(1)
- 最坏情况比较次数: 45
- 实际比较次数: 45
- 实际交换次数: 21

Spark RDD分区信息:
分区 0: 5 个元素
分区 1: 5 个元素

大数据集性能测试:
大数据集规模: 100 个元素
Spark分布式排序耗时: 156 毫秒
前10个元素: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
后10个元素: [91, 92, 93, 94, 95, 96, 97, 98, 99, 100]

============================================================
           实验完成!
============================================================
```

#### 步骤5：验证集群状态和监控
```bash
# 查看所有Java进程
jps

# 应该看到以下进程：
# - NameNode (Hadoop HDFS)
# - DataNode (Hadoop HDFS)
# - SecondaryNameNode (Hadoop HDFS)
# - ResourceManager (Hadoop YARN)
# - NodeManager (Hadoop YARN)
# - Master (Spark)
# - Worker (Spark)

# 查看HDFS状态
hdfs dfsadmin -report

# 查看YARN节点状态
yarn node -list

# 访问Web监控界面
echo "=== 监控界面地址 ==="
echo "Hadoop HDFS: http://localhost:9870"
echo "Hadoop YARN: http://localhost:8088"
echo "Spark Master: http://localhost:8080"
echo "Spark Application: http://localhost:4040"
echo "====================="
```

## 4、总结与反思

通过本次大数据平台搭建和冒泡排序算法实现实验，我获得了全面的技术实践经验和深刻的理论理解。

### 遇到的主要难点

1. **环境配置复杂性**：Hadoop和Spark涉及多个配置文件，需要正确设置Java路径、网络端口、内存分配等参数。特别是环境变量的配置需要确保在不同终端会话中都能生效。在配置过程中遇到了用户权限问题，需要设置HDFS_NAMENODE_USER等环境变量。

2. **服务依赖关系**：Spark依赖于Hadoop的HDFS，需要按正确顺序启动各个服务。SSH免密登录的配置对于集群通信至关重要，必须确保localhost能够免密访问。

3. **Scala语言特性**：Scala的函数式编程范式与传统命令式编程有很大差异，需要理解不可变数据结构、高阶函数等概念。在实现过程中发现Scala没有break语句，需要用其他方式实现循环控制。

4. **内存和性能调优**：在有限的系统资源下合理分配Spark的driver和executor内存，避免OutOfMemory错误。需要根据实际硬件配置调整内存参数。

5. **代码编译和运行**：最初尝试使用spark-submit直接运行Scala源文件失败，学会了使用Spark Shell进行交互式开发和测试的方法。

### 主要收获

1. **系统架构理解**：深入理解了Hadoop生态系统的架构，包括HDFS的分布式存储机制、YARN的资源管理模式，以及各组件之间的协作关系。

2. **Spark编程技能**：掌握了Spark RDD的创建、转换和行动操作，理解了惰性求值和分区的概念。学会了使用Spark Shell进行交互式开发。

3. **算法实现能力**：通过冒泡排序的实现，加深了对算法时间复杂度O(n²)和空间复杂度O(1)的理解。掌握了算法性能分析的方法。

4. **性能分析技能**：学会了比较不同算法和框架的性能特点，理解了分布式计算的优势和适用场景。通过对比小数据集和大数据集的处理，认识到技术选择的重要性。

5. **问题解决能力**：在配置过程中遇到的各种错误锻炼了查看日志、分析问题和解决故障的能力。学会了从错误信息中定位问题根源。

### 技术能力提升

1. **Linux系统管理**：熟练使用命令行进行软件安装、配置文件编辑、服务管理。掌握了yum包管理、systemctl服务管理等技能。

2. **大数据平台运维**：掌握了Hadoop和Spark集群的部署、启动、监控和基本运维。了解了分布式系统的配置要点。

3. **函数式编程**：理解了Scala的语法特性和编程范式，能够编写简洁高效的函数式代码。体验了不可变数据结构的优势。

4. **分布式计算**：理解了数据分区、并行处理、容错机制等分布式计算的核心概念。学会了使用RDD进行分布式数据处理。

### 实验创新点

1. **详细过程展示**：在冒泡排序实现中添加了详细的过程输出，便于理解算法执行的每一步，包括比较次数和交换次数的统计。

2. **分布式验证**：结合了Spark RDD进行结果验证，体现了分布式计算的实际应用，展示了传统算法与现代大数据技术的结合。

3. **性能对比分析**：进行了小数据集和大数据集的性能对比，展示了不同场景下的技术选择策略，体现了实际应用的考虑因素。

4. **分区信息展示**：添加了Spark RDD分区信息的展示，帮助理解分布式计算中数据分布的概念。

5. **多维度分析**：从算法复杂度、实际性能、分布式特性等多个维度分析问题，体现了全面的技术思考。

### 下一步学习规划

1. **深入Spark生态**：学习Spark SQL处理结构化数据，Spark Streaming处理实时数据流，MLlib进行机器学习建模，GraphX进行图计算。

2. **算法优化研究**：实现快速排序、归并排序、堆排序等高效算法，在分布式环境下进行性能对比分析，研究算法在大数据环境下的适用性。

3. **集群扩展实践**：搭建真正的多节点集群，学习集群扩容、负载均衡、故障恢复等高级运维技能，了解生产环境的部署要求。

4. **实际项目应用**：结合真实业务数据，开发完整的数据处理流水线，包括数据采集、清洗、分析、可视化等环节。

5. **性能调优深入**：学习JVM调优、Spark参数调优、数据倾斜处理等高级优化技术，提升系统性能和稳定性。

6. **新技术探索**：关注Spark 3.x的新特性，学习Delta Lake、Apache Iceberg等数据湖技术，探索云原生大数据解决方案。

### 实验总结

这次实验不仅让我掌握了大数据技术栈的核心组件和开发技能，更重要的是培养了系统性思维和工程实践能力。通过理论学习与实际操作的结合，深入理解了分布式计算的原理和应用场景。

冒泡排序作为一个经典的算法案例，虽然在实际应用中效率不高，但通过在Spark环境下的实现，让我体验了传统算法与现代大数据技术的结合。这种结合不仅展示了技术的演进，也让我思考如何在不同场景下选择合适的技术方案。

实验过程中遇到的各种技术挑战和解决方案，为我未来在大数据领域的深入发展和实际项目应用奠定了坚实的技术基础。同时，通过完整的实验报告撰写，也提升了我的技术文档编写和知识总结能力。
```
```
```

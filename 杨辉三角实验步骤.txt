题目1：打印杨辉三角实验步骤

一、实验目标
定义一个取值函数pascal，得到杨辉三角指定行列的值。杨辉三角的性质决定了除左右两边等于1外，元素的值等于其肩上两数的和，即Value(row,column）= Value(row-1,column-1)+Value(row-1,column)。以行为分界，打印每个元素的值。

二、实验环境准备

1. 检查系统环境
# 检查CentOS版本
cat /etc/redhat-release

# 检查Java环境
java -version
echo $JAVA_HOME

# 检查Hadoop环境
echo $HADOOP_HOME
hdfs version

# 检查Spark环境
echo $SPARK_HOME
spark-shell --version

2. 启动Hadoop集群
# 启动HDFS
start-dfs.sh

# 启动YARN
start-yarn.sh

# 检查服务状态
jps

3. 启动Spark集群
# 启动Spark Master
$SPARK_HOME/sbin/start-master.sh

# 启动Spark Worker
$SPARK_HOME/sbin/start-worker.sh spark://localhost:7077

# 检查Spark服务
jps | grep -E "(Master|Worker)"

三、启动Spark Shell
在CentOS终端中执行以下命令启动Spark Shell：

# 方式1：本地模式启动
spark-shell --master local[2] --driver-memory 1g --executor-memory 512m

# 方式2：连接到Spark集群
spark-shell --master spark://localhost:7077 --driver-memory 1g --executor-memory 512m

# 如果出现内存不足，可以调整参数
spark-shell --master local[1] --driver-memory 512m --executor-memory 256m

四、在CentOS中的Scala代码实现

启动Spark Shell后，在scala>提示符下逐行输入以下代码：

1. 定义pascal函数（递归实现）
scala> def pascal(row: Int, col: Int): Int = {
     |   if (col == 0 || col == row) 1
     |   else pascal(row - 1, col - 1) + pascal(row - 1, col)
     | }

# 输入完成后按回车，系统会显示：
# pascal: (row: Int, col: Int)Int

2. 定义打印杨辉三角的函数
scala> def printPascalTriangle(numRows: Int): Unit = {
     |   println("杨辉三角形：")
     |   for (row <- 0 until numRows) {
     |     // 打印前导空格，使三角形居中对齐
     |     val spaces = " " * (numRows - row - 1)
     |     print(spaces)
     |
     |     // 打印当前行的所有元素
     |     for (col <- 0 to row) {
     |       val value = pascal(row, col)
     |       print(s"$value ")
     |     }
     |     println() // 换行
     |   }
     | }

# 系统显示：
# printPascalTriangle: (numRows: Int)Unit

3. 定义优化版本（动态规划实现）
scala> def pascalOptimized(row: Int, col: Int): Int = {
     |   val triangle = Array.ofDim[Int](row + 1, row + 1)
     |
     |   for (r <- 0 to row) {
     |     for (c <- 0 to r) {
     |       if (c == 0 || c == r) {
     |         triangle(r)(c) = 1
     |       } else {
     |         triangle(r)(c) = triangle(r - 1)(c - 1) + triangle(r - 1)(c)
     |       }
     |     }
     |   }
     |   triangle(row)(col)
     | }

# 系统显示：
# pascalOptimized: (row: Int, col: Int)Int

4. 定义使用Spark RDD的分布式实现
scala> def pascalWithSpark(numRows: Int): Unit = {
     |   println("使用Spark RDD实现杨辉三角：")
     |
     |   for (row <- 0 until numRows) {
     |     // 创建当前行的列索引RDD
     |     val colIndices = sc.parallelize(0 to row)
     |
     |     // 使用map操作计算每个位置的值
     |     val rowValues = colIndices.map(col => pascal(row, col))
     |
     |     // 收集结果并打印
     |     val values = rowValues.collect()
     |     val spaces = " " * (numRows - row - 1)
     |     println(spaces + values.mkString(" "))
     |   }
     | }

# 系统显示：
# pascalWithSpark: (numRows: Int)Unit

注意：sc是Spark Shell中预定义的SparkContext对象

五、在CentOS中执行测试

在Spark Shell的scala>提示符下依次执行以下测试：

1. 测试基本pascal函数
scala> println("测试pascal函数：")
scala> println(s"pascal(0, 0) = ${pascal(0, 0)}")
scala> println(s"pascal(1, 0) = ${pascal(1, 0)}")
scala> println(s"pascal(1, 1) = ${pascal(1, 1)}")
scala> println(s"pascal(2, 1) = ${pascal(2, 1)}")
scala> println(s"pascal(4, 2) = ${pascal(4, 2)}")

2. 打印完整的杨辉三角
scala> printPascalTriangle(8)

3. 测试优化版本
scala> println("测试优化版本：")
scala> println(s"pascalOptimized(4, 2) = ${pascalOptimized(4, 2)}")

4. 测试Spark分布式版本
scala> pascalWithSpark(6)

5. 性能对比测试
scala> println("性能对比测试：")
scala> val startTime1 = System.currentTimeMillis()
scala> val result1 = pascal(10, 5)
scala> val endTime1 = System.currentTimeMillis()
scala> println(s"递归版本 pascal(10, 5) = $result1, 耗时: ${endTime1 - startTime1}ms")

scala> val startTime2 = System.currentTimeMillis()
scala> val result2 = pascalOptimized(10, 5)
scala> val endTime2 = System.currentTimeMillis()
scala> println(s"优化版本 pascalOptimized(10, 5) = $result2, 耗时: ${endTime2 - startTime2}ms")

6. 检查Spark作业执行情况
在CentOS中打开新的终端窗口，执行：
curl -s http://localhost:4040 | grep -i "jobs\|stages" || echo "Spark UI可能未启动"

或者在有图形界面的情况下访问：
http://localhost:4040

六、预期输出结果

测试pascal函数：
pascal(0, 0) = 1
pascal(1, 0) = 1
pascal(1, 1) = 1
pascal(2, 1) = 2
pascal(4, 2) = 6

杨辉三角形：
       1 
      1 1 
     1 2 1 
    1 3 3 1 
   1 4 6 4 1 
  1 5 10 10 5 1 
 1 6 15 20 15 6 1 
1 7 21 35 35 21 7 1 

使用Spark RDD实现杨辉三角：
     1
    1 1
   1 2 1
  1 3 3 1
 1 4 6 4 1
1 5 10 10 5 1

性能对比测试：
递归版本 pascal(10, 5) = 252, 耗时: 2ms
优化版本 pascalOptimized(10, 5) = 252, 耗时: 1ms

七、算法分析

1. 时间复杂度分析
递归版本：O(2^n)，存在大量重复计算
优化版本：O(n^2)，使用动态规划避免重复计算

2. 空间复杂度分析
递归版本：O(n)，递归调用栈的深度
优化版本：O(n^2)，需要存储整个三角形

3. 分布式特性
Spark版本利用了RDD的并行计算能力，适合处理大规模数据

八、在CentOS中的实验验证

1. 验证数学性质
在Spark Shell中执行：
scala> // 验证边界条件
scala> println("验证边界条件：")
scala> for (i <- 0 to 5) println(s"pascal($i, 0) = ${pascal(i, 0)}")
scala> for (i <- 0 to 5) println(s"pascal($i, $i) = ${pascal(i, i)}")

scala> // 验证递推关系
scala> println("验证递推关系：")
scala> val row = 4; val col = 2
scala> val current = pascal(row, col)
scala> val sum = pascal(row-1, col-1) + pascal(row-1, col)
scala> println(s"pascal($row, $col) = $current")
scala> println(s"pascal(${row-1}, ${col-1}) + pascal(${row-1}, $col) = $sum")
scala> println(s"验证结果: ${current == sum}")

2. 验证Spark集群状态
在CentOS终端中执行：
# 检查Spark进程
jps | grep -E "(Master|Worker|SparkSubmit)"

# 检查端口占用
netstat -tlnp | grep -E "(4040|7077|8080)"

# 使用curl检查Web界面
curl -I http://localhost:4040 2>/dev/null | head -1
curl -I http://localhost:8080 2>/dev/null | head -1

3. 验证RDD操作
在Spark Shell中执行：
scala> // 创建简单RDD验证
scala> val testRDD = sc.parallelize(1 to 10)
scala> println(s"RDD分区数: ${testRDD.getNumPartitions}")
scala> println(s"RDD元素: ${testRDD.collect().mkString(", ")}")

4. 检查系统资源使用
在CentOS终端中执行：
# 检查内存使用
free -h

# 检查CPU使用
top -n 1 | head -5

# 检查磁盘使用
df -h

九、退出Spark Shell和关闭服务
完成实验后，按以下步骤退出：

1. 退出Spark Shell
scala> :quit

2. 关闭Spark集群（可选）
# 停止Spark Worker
$SPARK_HOME/sbin/stop-worker.sh

# 停止Spark Master
$SPARK_HOME/sbin/stop-master.sh

3. 关闭Hadoop集群（可选）
# 停止YARN
stop-yarn.sh

# 停止HDFS
stop-dfs.sh

4. 验证服务已关闭
jps

十、实验总结
通过本实验掌握了递归算法的Scala实现，理解了动态规划优化思想，学会了使用Spark RDD进行分布式计算，体验了函数式编程在算法实现中的优势。

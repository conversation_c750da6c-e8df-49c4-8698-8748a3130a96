题目1：打印杨辉三角实验步骤

一、实验目标
定义一个取值函数pascal，得到杨辉三角指定行列的值。杨辉三角的性质决定了除左右两边等于1外，元素的值等于其肩上两数的和，即Value(row,column）= Value(row-1,column-1)+Value(row-1,column)。以行为分界，打印每个元素的值。

二、实验环境准备
确保Hadoop和Spark集群已正常启动，可以参考之前的环境搭建步骤。

三、启动Spark Shell
在终端中执行以下命令启动Spark Shell：
spark-shell --master local[2] --driver-memory 1g --executor-memory 512m

四、Scala代码实现

1. 定义pascal函数（递归实现）
def pascal(row: Int, col: Int): Int = {
  if (col == 0 || col == row) 1
  else pascal(row - 1, col - 1) + pascal(row - 1, col)
}

2. 定义打印杨辉三角的函数
def printPascalTriangle(numRows: Int): Unit = {
  println("杨辉三角形：")
  for (row <- 0 until numRows) {
    // 打印前导空格，使三角形居中对齐
    val spaces = " " * (numRows - row - 1)
    print(spaces)
    
    // 打印当前行的所有元素
    for (col <- 0 to row) {
      val value = pascal(row, col)
      print(s"$value ")
    }
    println() // 换行
  }
}

3. 定义优化版本（动态规划实现）
def pascalOptimized(row: Int, col: Int): Int = {
  val triangle = Array.ofDim[Int](row + 1, row + 1)
  
  for (r <- 0 to row) {
    for (c <- 0 to r) {
      if (c == 0 || c == r) {
        triangle(r)(c) = 1
      } else {
        triangle(r)(c) = triangle(r - 1)(c - 1) + triangle(r - 1)(c)
      }
    }
  }
  triangle(row)(col)
}

4. 定义使用Spark RDD的分布式实现
def pascalWithSpark(numRows: Int): Unit = {
  println("使用Spark RDD实现杨辉三角：")
  
  for (row <- 0 until numRows) {
    // 创建当前行的列索引RDD
    val colIndices = sc.parallelize(0 to row)
    
    // 使用map操作计算每个位置的值
    val rowValues = colIndices.map(col => pascal(row, col))
    
    // 收集结果并打印
    val values = rowValues.collect()
    val spaces = " " * (numRows - row - 1)
    println(spaces + values.mkString(" "))
  }
}

五、执行测试

1. 测试基本pascal函数
println("测试pascal函数：")
println(s"pascal(0, 0) = ${pascal(0, 0)}")
println(s"pascal(1, 0) = ${pascal(1, 0)}")
println(s"pascal(1, 1) = ${pascal(1, 1)}")
println(s"pascal(2, 1) = ${pascal(2, 1)}")
println(s"pascal(4, 2) = ${pascal(4, 2)}")

2. 打印完整的杨辉三角
printPascalTriangle(8)

3. 测试优化版本
println("\n测试优化版本：")
println(s"pascalOptimized(4, 2) = ${pascalOptimized(4, 2)}")

4. 测试Spark分布式版本
pascalWithSpark(6)

5. 性能对比测试
println("\n性能对比测试：")
val startTime1 = System.currentTimeMillis()
val result1 = pascal(10, 5)
val endTime1 = System.currentTimeMillis()
println(s"递归版本 pascal(10, 5) = $result1, 耗时: ${endTime1 - startTime1}ms")

val startTime2 = System.currentTimeMillis()
val result2 = pascalOptimized(10, 5)
val endTime2 = System.currentTimeMillis()
println(s"优化版本 pascalOptimized(10, 5) = $result2, 耗时: ${endTime2 - startTime2}ms")

六、预期输出结果

测试pascal函数：
pascal(0, 0) = 1
pascal(1, 0) = 1
pascal(1, 1) = 1
pascal(2, 1) = 2
pascal(4, 2) = 6

杨辉三角形：
       1 
      1 1 
     1 2 1 
    1 3 3 1 
   1 4 6 4 1 
  1 5 10 10 5 1 
 1 6 15 20 15 6 1 
1 7 21 35 35 21 7 1 

使用Spark RDD实现杨辉三角：
     1
    1 1
   1 2 1
  1 3 3 1
 1 4 6 4 1
1 5 10 10 5 1

性能对比测试：
递归版本 pascal(10, 5) = 252, 耗时: 2ms
优化版本 pascalOptimized(10, 5) = 252, 耗时: 1ms

七、算法分析

1. 时间复杂度分析
递归版本：O(2^n)，存在大量重复计算
优化版本：O(n^2)，使用动态规划避免重复计算

2. 空间复杂度分析
递归版本：O(n)，递归调用栈的深度
优化版本：O(n^2)，需要存储整个三角形

3. 分布式特性
Spark版本利用了RDD的并行计算能力，适合处理大规模数据

八、实验验证

1. 验证数学性质
检查边界条件：第一列和对角线都为1
检查递推关系：内部元素等于上方两个元素之和

2. 验证Spark集群状态
访问 http://localhost:4040 查看Spark应用程序界面
观察作业执行情况和RDD操作

九、退出Spark Shell
完成实验后，输入以下命令退出：
:quit

十、实验总结
通过本实验掌握了递归算法的Scala实现，理解了动态规划优化思想，学会了使用Spark RDD进行分布式计算，体验了函数式编程在算法实现中的优势。
